import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { getSessionByToken, getUserById } from '@/lib/auth-utils';
import { supabaseAdmin } from '@/lib/supabase-server';

export async function GET(_request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = await cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Using supabaseAdmin imported from supabase-server

    // First, ensure the user has a profile record
    const { error: profileError } = await supabaseAdmin
      .from('profiles')
      .upsert({
        id: user.id,
        email: user.email,
        subscription_tier: 'free',
        subscription_status: 'active',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }, { onConflict: 'id' });

    if (profileError) {
      console.error('Error creating profile:', profileError);
      // Continue anyway, as we'll try to fetch services
    }

    const userId = user.id;

    // Get services for the user
    const { data, error } = await supabaseAdmin
      .from('service_sunset')
      .select('*')
      .eq('user_id', userId)
      .order('name', { ascending: true });

    if (error) {
      console.error('Error fetching services:', error);
      return NextResponse.json(
        { error: 'Failed to fetch services' },
        { status: 500 }
      );
    }

    return NextResponse.json(data || []);
  } catch (error: any) {
    console.error('Error fetching services:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch services' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = await cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse the request body
    const body = await request.json();

    // Validate required fields
    if (!body.name || !body.category) {
      return NextResponse.json(
        { error: 'Name and category are required' },
        { status: 400 }
      );
    }

    // Ensure the name has its first letter capitalized
    if (body.name) {
      body.name = body.name.charAt(0).toUpperCase() + body.name.slice(1);
    }

    // Add the user_id to the service data
    const serviceData = {
      ...body,
      user_id: user.id,
      // Set default values for required database fields
      cancellation_method: body.cancellation_method || 'online',
      priority: body.priority || 'medium',
      auto_renewal: body.auto_renewal || false
    };

    try {
      // Using supabaseAdmin imported from supabase-server

      // First, try to create a profile for this user to avoid foreign key issues
      // This is a workaround for the foreign key constraint issue
      const { error: profileError } = await supabaseAdmin
        .from('profiles')
        .upsert({
          id: user.id,
          email: user.email,
          subscription_tier: 'free',
          subscription_status: 'active',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }, { onConflict: 'id' });

      if (profileError) {
        console.error('Error creating profile:', profileError);
        // Continue anyway, as we'll try to insert the service
      }

      // Now try to insert the service
      const { data, error: insertError } = await supabaseAdmin
        .from('service_sunset')
        .insert(serviceData)
        .select();

      if (insertError) {
        console.error('Error creating service:', insertError);
        return NextResponse.json(
          { error: `Failed to create service: ${insertError.message}` },
          { status: 500 }
        );
      }

      if (!data || data.length === 0) {
        return NextResponse.json(
          { error: 'No data returned after service creation' },
          { status: 500 }
        );
      }

      return NextResponse.json(data[0]);
    } catch (insertError: any) {
      console.error('Exception creating service:', insertError);
      return NextResponse.json(
        { error: `Exception creating service: ${insertError.message}` },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error('Error adding service:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to add service' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = await cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse the request body
    const { id, ...serviceData } = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: 'Service ID is required' },
        { status: 400 }
      );
    }

    // Validate required fields
    if (!serviceData.name || !serviceData.category) {
      return NextResponse.json(
        { error: 'Name and category are required' },
        { status: 400 }
      );
    }

    // Ensure the name has its first letter capitalized
    if (serviceData.name) {
      serviceData.name = serviceData.name.charAt(0).toUpperCase() + serviceData.name.slice(1);
    }

    // Using supabaseAdmin imported from supabase-server

    const userId = user.id;

    // First, verify that the service belongs to the user
    const { error: fetchError } = await supabaseAdmin
      .from('service_sunset')
      .select('id')
      .eq('id', id)
      .eq('user_id', userId)
      .single();

    if (fetchError) {
      console.error('Error fetching service:', fetchError);
      return NextResponse.json(
        { error: 'Service not found or you do not have permission to update it' },
        { status: 404 }
      );
    }

    // Ensure required fields have default values
    serviceData.cancellation_method = serviceData.cancellation_method || 'online';
    serviceData.priority = serviceData.priority || 'medium';

    // Update the service
    const { data, error: updateError } = await supabaseAdmin
      .from('service_sunset')
      .update({
        ...serviceData,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .eq('user_id', userId)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating service:', updateError);
      return NextResponse.json(
        { error: `Failed to update service: ${updateError.message}` },
        { status: 500 }
      );
    }

    return NextResponse.json(data);
  } catch (error: any) {
    console.error('Error updating service:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to update service' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = await cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the service ID from the URL
    const url = new URL(request.url);
    const id = url.searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'Service ID is required' },
        { status: 400 }
      );
    }

    // Using supabaseAdmin imported from supabase-server

    const userId = user.id;

    // First, verify that the service belongs to the user
    const { error: fetchError } = await supabaseAdmin
      .from('service_sunset')
      .select('id')
      .eq('id', id)
      .eq('user_id', userId)
      .single();

    if (fetchError) {
      console.error('Error fetching service:', fetchError);
      return NextResponse.json(
        { error: 'Service not found or you do not have permission to delete it' },
        { status: 404 }
      );
    }

    // Delete the service
    const { error: deleteError } = await supabaseAdmin
      .from('service_sunset')
      .delete()
      .eq('id', id)
      .eq('user_id', userId);

    if (deleteError) {
      console.error('Error deleting service:', deleteError);
      return NextResponse.json(
        { error: `Failed to delete service: ${deleteError.message}` },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Error deleting service:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to delete service' },
      { status: 500 }
    );
  }
}
